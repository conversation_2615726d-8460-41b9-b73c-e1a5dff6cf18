<template>
  <div class="dashboard-container">
    <!-- Header -->
    <div class="header">
      <h1 class="page-title">
        <img src="@/assets/logo/pufa_logo.png" alt="logo" />
        普法工作管理系统
      </h1>
      <div class="current-date">{{ currentDate }}</div>
    </div>

    <!-- Stats Cards -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="12">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-title">平台收集官方信息总数</div>
          <div class="stat-value">12,345</div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="stat-card">
          <div class="stat-title">今日增加数</div>
          <div class="stat-value">678</div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Charts Grid -->
    <el-row :gutter="20" class="charts-grid">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">各类型单位今日发布数</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('barChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="barChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">top10本周各单位发布数统计</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('horizontalBarChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="horizontalBarChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="20" class="charts-grid">
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">各来源今日发布数</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('pieChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="pieChart" class="chart-container"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card shadow="hover" class="chart-card">
          <template #header>
            <div class="chart-header">
              <span class="chart-title">top5各普法内容类型本季度提及数趋势</span>
              <el-button
                class="detail-btn"
                type="primary"
                size="small"
                @click="showDetail('lineChart')"
                :icon="InfoFilled"
              >
                详情
              </el-button>
            </div>
          </template>
          <div ref="lineChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- Detail Dialog -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="80%"
      :before-close="handleClose"
    >
      <el-table :data="tableData" style="width: 100%" border>
        <el-table-column prop="unitType" label="单位属性" width="120" />
        <el-table-column prop="unitName" label="单位名称" width="200" />
        <el-table-column prop="articleCount" label="文章数" width="100" />
        <el-table-column prop="lastPublishTime" label="最新文章发布时间" width="180" />
        <el-table-column label="查看" width="100">
          <template #default="scope">
            <el-button type="text" size="small" @click="viewDetail(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup name="Index">
import { ref, onMounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import { InfoFilled } from '@element-plus/icons-vue';

const currentDate = ref('');

// Dialog related data
const dialogVisible = ref(false);
const dialogTitle = ref('');
const tableData = ref([]);

// Function to format date
const formatDate = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  currentDate.value = `${year}年${month}月${day}日`;
};

// Chart element refs
const barChart = ref(null);
const horizontalBarChart = ref(null);
const pieChart = ref(null);
const lineChart = ref(null);

// Init Bar Chart
const initBarChart = () => {
  const chart = echarts.init(barChart.value);
  const option = {
    tooltip: {},
    xAxis: {
      data: ['市政府', '区政府', '国企', '高校']
    },
    yAxis: {},
    series: [{
      name: '发布数',
      type: 'line',
      data: [11, 18, 15, 10],
      itemStyle: {
        color: '#5470c6'
      }
    }]
  };
  chart.setOption(option);
};

// Init Horizontal Bar Chart
const initHorizontalBarChart = () => {
  const chart = echarts.init(horizontalBarChart.value);
  const option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
    yAxis: {
      type: 'category',
      data: ['单位Z', '单位Y', '单位X', '单位W', '单位V']
    },
    xAxis: { type: 'value' },
    series: [{
      name: '发布数',
      type: 'bar',
      data: [180, 210, 240, 280, 320],
      itemStyle: {
        color: '#91cc75'
      }
    }]
  };
  chart.setOption(option);
};

// Init Pie Chart
const initPieChart = () => {
  const chart = echarts.init(pieChart.value);
  const option = {
    tooltip: { trigger: 'item' },
    series: [{
      name: '来源',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: '来源1' },
        { value: 735, name: '来源2' },
        { value: 580, name: '来源3' },
        { value: 484, name: '来源4' },
        { value: 300, name: '来源5' }
      ]
    }]
  };
  chart.setOption(option);
};

// Init Line Chart
const initLineChart = () => {
  const chart = echarts.init(lineChart.value);
  const option = {
    tooltip: { trigger: 'axis' },
    legend: {
      top: '5%'
    },
    grid: {
      top: '15%'
    },
    xAxis: {
      type: 'category',
      data: ['1月', '2月', '3月']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '类型一',
      type: 'line',
      data: [150, 230, 224]
    }, {
      name: '类型二',
      type: 'line',
      data: [120, 182, 191]
    }]
  };
  chart.setOption(option);
};

// Mock data for different chart details
const chartDetailData = {
  barChart: [
    { unitType: '市政府', unitName: '市政府办公室', articleCount: 5, lastPublishTime: '2024-01-15 14:30' },
    { unitType: '市政府', unitName: '市发改委', articleCount: 3, lastPublishTime: '2024-01-15 13:20' },
    { unitType: '市政府', unitName: '市财政局', articleCount: 3, lastPublishTime: '2024-01-15 12:10' },
    { unitType: '区政府', unitName: '朝阳区政府', articleCount: 8, lastPublishTime: '2024-01-15 15:45' },
    { unitType: '区政府', unitName: '海淀区政府', articleCount: 6, lastPublishTime: '2024-01-15 14:15' },
    { unitType: '区政府', unitName: '西城区政府', articleCount: 4, lastPublishTime: '2024-01-15 13:30' },
    { unitType: '国企', unitName: '国家电网', articleCount: 7, lastPublishTime: '2024-01-15 16:20' },
    { unitType: '国企', unitName: '中石油', articleCount: 5, lastPublishTime: '2024-01-15 15:10' },
    { unitType: '国企', unitName: '中石化', articleCount: 3, lastPublishTime: '2024-01-15 14:40' },
    { unitType: '高校', unitName: '清华大学', articleCount: 4, lastPublishTime: '2024-01-15 16:00' },
    { unitType: '高校', unitName: '北京大学', articleCount: 3, lastPublishTime: '2024-01-15 15:30' },
    { unitType: '高校', unitName: '人民大学', articleCount: 3, lastPublishTime: '2024-01-15 14:50' }
  ],
  horizontalBarChart: [
    { unitType: '政府机关', unitName: '单位V', articleCount: 320, lastPublishTime: '2024-01-15 16:30' },
    { unitType: '政府机关', unitName: '单位W', articleCount: 280, lastPublishTime: '2024-01-15 15:20' },
    { unitType: '事业单位', unitName: '单位X', articleCount: 240, lastPublishTime: '2024-01-15 14:10' },
    { unitType: '事业单位', unitName: '单位Y', articleCount: 210, lastPublishTime: '2024-01-15 13:40' },
    { unitType: '国有企业', unitName: '单位Z', articleCount: 180, lastPublishTime: '2024-01-15 12:50' }
  ],
  pieChart: [
    { unitType: '官方网站', unitName: '来源1', articleCount: 1048, lastPublishTime: '2024-01-15 16:45' },
    { unitType: '微信公众号', unitName: '来源2', articleCount: 735, lastPublishTime: '2024-01-15 15:35' },
    { unitType: '官方微博', unitName: '来源3', articleCount: 580, lastPublishTime: '2024-01-15 14:25' },
    { unitType: '新闻媒体', unitName: '来源4', articleCount: 484, lastPublishTime: '2024-01-15 13:15' },
    { unitType: '其他平台', unitName: '来源5', articleCount: 300, lastPublishTime: '2024-01-15 12:05' }
  ],
  lineChart: [
    { unitType: '法律法规', unitName: '类型一', articleCount: 224, lastPublishTime: '2024-03-15 16:20' },
    { unitType: '案例分析', unitName: '类型二', articleCount: 191, lastPublishTime: '2024-03-15 15:10' },
    { unitType: '政策解读', unitName: '类型三', articleCount: 156, lastPublishTime: '2024-03-15 14:30' },
    { unitType: '普法宣传', unitName: '类型四', articleCount: 128, lastPublishTime: '2024-03-15 13:45' }
  ]
};

// Chart titles mapping
const chartTitles = {
  barChart: '各类型单位今日发布内容详情',
  horizontalBarChart: 'top10本周各单位发布内容详情',
  pieChart: '各来源今日发布内容详情',
  lineChart: 'top5各普法内容类型本季度详情趋势'
};

// Show detail dialog
const showDetail = (chartType) => {
  dialogTitle.value = chartTitles[chartType];
  tableData.value = chartDetailData[chartType] || [];
  dialogVisible.value = true;
};

// Handle dialog close
const handleClose = (done) => {
  dialogVisible.value = false;
  if (done) done();
};

// View detail action
const viewDetail = (row) => {
  console.log('查看详情:', row);
  // 这里可以添加查看详情的逻辑，比如跳转到详情页面
};

onMounted(() => {
  formatDate();
  setInterval(formatDate, 1000 * 60 * 60); // Update every hour is enough for date
  nextTick(() => {
    initBarChart();
    initHorizontalBarChart();
    initPieChart();
    initLineChart();
  });
});
</script>

<style scoped lang="scss">
.dashboard-container {
  padding: 32px;
  background-color: #f0f2f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
}

.page-title img {
  height: 28px; /* Match font-size of title */
  margin-right: 10px;
}

.current-date {
  font-size: 22px;
  color: #606266;
  font-weight: 500;
  letter-spacing: 1px;
}

.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  border-radius: 12px;
  .stat-title {
    font-size: 16px;
    color: #909399;
    margin-bottom: 12px;
  }
  .stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
  }
}

.charts-grid {
  margin-top: 20px;
  margin-bottom: 20px;
}

.chart-card {
  border-radius: 12px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fafbfc;
  border-radius: 8px 8px 0 0;
  margin: -20px -20px 16px -20px;
  padding: 16px 20px;
  border-bottom: 2px solid #e1e8ed;
}

.chart-title {
  font-size: 18px;
  font-weight: 800;
  color: #1a202c;
  letter-spacing: 0.5px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

.detail-btn {
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  background: #409eff;
  border: 1px solid #409eff;
  color: #ffffff;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
  user-select: none;

  &:hover {
    background: #66b1ff;
    border-color: #66b1ff;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
  }

  .el-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  // 响应式设计
  @media (max-width: 768px) {
    padding: 6px 12px;
    font-size: 12px;

    .el-icon {
      font-size: 12px;
      margin-right: 3px;
    }
  }
}





.chart-container {
  width: 100%;
  height: 350px;
}

// Dialog styles
:deep(.el-dialog__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

:deep(.el-table) {
  .el-table__header {
    background-color: #fafafa;
  }

  .el-table__row:hover {
    background-color: #f5f7fa;
  }
}
</style>
