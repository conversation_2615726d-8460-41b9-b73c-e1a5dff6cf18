# 一开始的数据，把Excel数据转到数据库的，过渡用

import os
from asyncio.log import logger
from datetime import datetime

import pandas as pd
from flask import request, jsonify, current_app, make_response
from flask_login import current_user, login_required
from web import db
from web.models.faxuan.FXAnalysusSummary import AnalysisSummary
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web.models.faxuan.FXEducationArticles import LegalEducationArticle
from web.routes import main_routes
from web.decorator import permission

# 上传表格信息保存到数据库
@main_routes.route('/data/excelUpload', methods=['POST'])
# @login_required
# @permission('system:excel:upload')
def upload_excel():
    logger = current_app.logger
    logger.debug(f"请求 URL: {request.url}")
    logger.debug(f"请求方法: {request.method}")
    logger.debug(f"请求头: {request.headers}")
    logger.debug(f"请求文件: {request.files}")

    # 读取文件
    file = request.files.get('file')
    if file is None or file.filename == '':
        logger.warning("未接收到文件或文件名为空")
        return jsonify({'message': '未选择文件'}), 400

    # 验证文件类型
    allowed_extensions = {'.xlsx', '.xls'}
    if not any(file.filename.lower().endswith(ext) for ext in allowed_extensions):
        logger.warning(f"文件类型不合法: {file.filename}")
        return jsonify({'message': '只允许上传 .xlsx 或 .xls 文件'}), 400

    # 清理文件名（仅用于日志）
    file_name = file.filename
    logger.debug(f"文件名: {file_name}")

    # 直接处理 Excel 文件流并保存到数据库
    try:
        # 上传原始的数据爬虫数据
        upload_excel_file(file)

        # 上传分析的结果
        # process_legal_excel(file)
        # process_analysis_summary_excel(file)
    except Exception as inter_e:
        error_message = str(inter_e)
        logger.error(f"处理 Excel 文件或数据库保存失败: {error_message}")
        return jsonify({'message': f'处理文件失败: {error_message}'}), 500

    return jsonify({
        'code': 200,
        'message': '文件上传成功'
    })

def upload_excel_data_save(file):
    logger = current_app.logger
    try:
        file.seek(0)  # 重置文件指针
        # 动态获取 Excel 的工作表名称
        try:
            xl = pd.ExcelFile(file, engine='openpyxl')
            if not xl.sheet_names:
                raise ValueError("Excel 文件不包含任何工作表")
            sheet_name = xl.sheet_names[0]  # 使用第一个工作表（Sheet1）
            logger.debug(f"可用工作表: {xl.sheet_names}, 选择的工作表: {sheet_name}")
        except Exception as e:
            logger.error(f"无法解析 Excel 文件: {str(e)}")
            raise ValueError(f"无法解析 Excel 文件: {str(e)}")

        # 读取 Excel 数据
        file.seek(0)  # 再次重置文件指针
        df = pd.read_excel(file, sheet_name=sheet_name, engine='openpyxl')
        # 清理列名（去除空格、BOM等）
        df.columns = [col.strip() if isinstance(col, str) else col for col in df.columns]
        logger.info(f"成功读取 Excel 文件，工作表: {sheet_name}, 列名: {list(df.columns)}")

        # 调试：打印前几行数据
        logger.debug(f"前5行数据:\n{df.head().to_dict(orient='records')}")

        # 支持多种可能的列名
        title_columns = ['文章标题', '标题', 'Title', 'Article Title']
        unit_columns = ['单位名称', 'Unit Name']
        channel_columns = ['爬取渠道', 'Crawl Channel']
        content_columns = ['文章内容', 'Article Content']
        publish_time_columns = ['文章发布时间', 'Publish Time']
        crawl_time_columns = ['爬取时间', 'Crawl Time']
        view_count_columns = ['浏览次数', 'View Count']
        url_columns = ['文章链接', 'Article URL']
        id_columns = ['文章id', '文章ID', 'Article ID']

        records = []
        for index, row in df.iterrows():
            # 获取必填字段
            article_title = None
            for col in title_columns:
                if col in df.columns:
                    article_title = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break

            unit_name = None
            for col in unit_columns:
                if col in df.columns:
                    unit_name = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break

            crawl_channel = None
            for col in channel_columns:
                if col in df.columns:
                    crawl_channel = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break

            if not article_title or not unit_name or not crawl_channel:
                logger.warning(f"第 {index + 2} 行缺少必填字段（文章标题: {article_title}, 单位名称: {unit_name}, 爬取渠道: {crawl_channel}），跳过")
                continue

            # 处理 crawl_time
            crawl_time = None
            crawl_time_str = ''
            for col in crawl_time_columns:
                if col in df.columns:
                    crawl_time_str = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            if crawl_time_str:
                try:
                    crawl_time = datetime.strptime(crawl_time_str, '%Y-%m-%d-%H:%M')
                except ValueError:
                    logger.warning(f"第 {index + 2} 行爬取时间格式无效: {crawl_time_str}，使用当前时间")
                    crawl_time = datetime.now()
            else:
                logger.debug(f"第 {index + 2} 行未提供爬取时间，使用当前时间")
                crawl_time = datetime.now()

            # 处理 publish_time
            publish_time = None
            publish_time_str = ''
            for col in publish_time_columns:
                if col in df.columns:
                    publish_time_str = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            if publish_time_str:
                try:
                    try:
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d-%H:%M')
                    except ValueError:
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"第 {index + 2} 行文章发布时间格式无效: {publish_time_str}")

            # 处理 view_count
            view_count = None
            view_count_str = ''
            for col in view_count_columns:
                if col in df.columns:
                    view_count_str = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            if view_count_str and view_count_str != '无':
                try:
                    view_count = int(float(view_count_str))
                except ValueError:
                    logger.warning(f"第 {index + 2} 行浏览次数格式无效: {view_count_str}")

            # 获取其他字段
            article_content = None
            for col in content_columns:
                if col in df.columns:
                    article_content = str(row.get(col, '')) if pd.notna(row.get(col)) else None
                    break

            article_url = None
            for col in url_columns:
                if col in df.columns:
                    article_url = str(row.get(col, '')) if pd.notna(row.get(col)) else None
                    break

            article_id = None
            for col in id_columns:
                if col in df.columns:
                    article_id = str(row.get(col, '')) if pd.notna(row.get(col)) else None
                    break

            record = FxArticleRecords(
                crawl_time=crawl_time,
                crawl_channel=crawl_channel,
                unit_name=unit_name,
                article_title=article_title,
                article_content=article_content,
                publish_time=publish_time,
                view_count=view_count,
                article_url=article_url,
                article_id=article_id,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            records.append(record)
            logger.debug(f"已添加记录，标题: {article_title}")

        if not records:
            logger.warning("没有有效的记录可以保存到数据库")
            raise ValueError("Excel 文件没有有效的记录（可能缺少必填字段或数据无效）")

        db.session.add_all(records)
        db.session.commit()
        logger.info(f"成功保存 {len(records)} 条记录到 fx_article_records 表")

    except Exception as e:
        logger.error(f"处理 Excel 文件失败: {str(e)}")
        db.session.rollback()
        raise



# 保存分析总结的表格数据（表4）
def process_analysis_summary_excel(file):
    """处理分析汇总表Excel文件并保存到数据库"""
    try:
        file.seek(0)
        df = pd.read_excel(file, engine='openpyxl')

        # 标准化列名
        df.columns = [col.strip() if isinstance(col, str) else col for col in df.columns]

        # 支持的列名映射
        column_mapping = {
            'unit_property': ['单位属性', 'Unit Property'],
            'industry_system': ['行业系统', 'Industry System'],
            'total_articles': ['发布文章总数', 'Total Articles'],
            'total_views': ['总阅读量', 'Total Views'],
            'max_article_views': ['单篇文章最高阅读量', 'Max Article Views'],
            'month': ['月份', 'Month'],
            'total_likes': ['总点赞量', 'Total Likes'],
            'max_article_likes': ['单篇文章最高点赞量', 'Max Article Likes'],
            'total_comments': ['总评论量', 'Total Comments'],
            'max_article_comments': ['单篇文章最高评论量', 'Max Article Comments']
        }

        records = []

        for _, row in df.iterrows():
            data = {}
            for field, possible_columns in column_mapping.items():
                value = None
                for col in possible_columns:
                    if col in df.columns and pd.notna(row[col]):
                        value = row[col]
                        break
                if value is None and field in ['unit_property', 'industry_system', 'total_articles',
                                              'total_views', 'max_article_views', 'month',
                                              'total_likes', 'max_article_likes',
                                              'total_comments', 'max_article_comments']:
                    raise ValueError(f"缺少必填字段: {field} 在行 {row.name + 2}")
                data[field] = value

            # 格式化月份为 YYYY-MM
            if isinstance(data['month'], (int, float)):
                data['month'] = f"2025-{int(data['month']):02d}"
            elif not isinstance(data['month'], str) or not data['month'].startswith('202'):
                raise ValueError(f"无效的月份格式: {data['month']} 在行 {row.name + 2}")

            # 确保数值字段为整数
            for field in ['total_articles', 'max_article_views', 'total_likes', 'max_article_likes',
                          'total_comments', 'max_article_comments']:
                if not isinstance(data[field], (int, float)) or pd.isna(data[field]):
                    raise ValueError(f"无效的数值字段 {field}: {data[field]} 在行 {row.name + 2}")
                data[field] = int(data[field])

            # 创建记录对象
            record = AnalysisSummary(
                unit_property=str(data['unit_property']).strip(),
                industry_system=str(data['industry_system']).strip(),
                total_articles=data['total_articles'],
                total_views=int(data['total_views']),
                max_article_views=data['max_article_views'],
                month=data['month'],
                total_likes=data['total_likes'],
                max_article_likes=data['max_article_likes'],
                total_comments=data['total_comments'],
                max_article_comments=data['max_article_comments'],
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            records.append(record)

        # 保存到数据库
        try:
            db.session.add_all(records)
            db.session.commit()
            current_app.logger.info(f"成功保存 {len(records)} 条记录到 fX_analysis_summary 表")
            return records
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存记录到数据库失败: {str(e)}")
            raise

    except Exception as e:
        current_app.logger.error(f"处理分析汇总表Excel失败: {str(e)}")
        raise
# 保存文章标签数据
def process_legal_excel(file):
    """处理普法教育Excel文件并保存到数据库"""
    try:
        file.seek(0)
        df = pd.read_excel(file, engine='openpyxl')

        # 标准化列名
        df.columns = [col.strip() if isinstance(col, str) else col for col in df.columns]

        # 支持的列名映射
        column_mapping = {
            'article_id': ['文章id', '文章ID', 'Article ID'],
            'unit_name': ['单位名称', 'Unit Name'],
            'unit_property': ['单位属性', 'Unit Property'],
            'industry_system': ['行业系统', 'Industry System'],
            'unit_district': ['单位所在区', 'District'],
            'legal_content_type': ['普法内容类型', 'Legal Content Type'],
            'target_group': ['受众群体', 'Target Group'],
            'people_scale': ['人数规模', 'People Scale']
        }

        records = []
        current_month = datetime.now().strftime('%Y-%m')

        for _, row in df.iterrows():
            data = {}
            for field, possible_columns in column_mapping.items():
                value = None
                for col in possible_columns:
                    if col in df.columns and pd.notna(row[col]):
                        value = str(row[col]).strip()
                        break
                if not value and field in ['article_id', 'unit_name', 'unit_property', 'industry_system',
                                         'unit_district', 'legal_content_type', 'target_group', 'people_scale']:
                    raise ValueError(f"缺少必填字段: {field} 在行 {row.name + 2}")
                data[field] = value if value else ''

            data['month'] = current_month

            record = LegalEducationArticle(
                article_id=data['article_id'],
                unit_name=data['unit_name'],
                unit_property=data['unit_property'],
                industry_system=data['industry_system'],
                unit_district=data['unit_district'],
                legal_content_type=data['legal_content_type'],
                target_group=data['target_group'],
                people_scale=data['people_scale'],
                month=data['month'],
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            records.append(record)

        # 保存到数据库
        try:
            db.session.add_all(records)
            db.session.commit()
            current_app.logger.info(f"成功保存 {len(records)} 条记录到数据库")
            return records
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"保存记录到数据库失败: {str(e)}")
            raise

    except Exception as e:
        current_app.logger.error(f"处理普法教育Excel失败: {str(e)}")
        raise

# 保存爬取的文章记录
def upload_excel_file(file):
    logger.info("开始处理上传的 Excel 文件")
    try:
        file.seek(0)  # 重置文件指针
        # 动态获取 Excel 的工作表名称
        try:
            xl = pd.ExcelFile(file, engine='openpyxl')
            if not xl.sheet_names:
                raise ValueError("Excel 文件不包含任何工作表")
            sheet_name = xl.sheet_names[0]  # 使用第一个工作表（Sheet1）
            logger.debug(f"可用工作表: {xl.sheet_names}, 选择的工作表: {sheet_name}")
        except Exception as e:
            logger.error(f"无法解析 Excel 文件: {str(e)}")
            raise ValueError(f"无法解析 Excel 文件: {str(e)}")

        # 读取 Excel 数据
        file.seek(0)  # 再次重置文件指针
        df = pd.read_excel(file, sheet_name=sheet_name, engine='openpyxl')
        # 清理列名（去除空格、BOM等）
        df.columns = [col.strip() if isinstance(col, str) else col for col in df.columns]
        logger.info(f"成功读取 Excel 文件，工作表: {sheet_name}, 列名: {list(df.columns)}")

        # 调试：打印前几行数据
        logger.debug(f"前5行数据:\n{df.head().to_dict(orient='records')}")

        # 支持多种可能的列名
        title_columns = ['文章标题', '标题', 'Title', 'Article Title']
        unit_columns = ['单位名称', 'Unit Name']
        channel_columns = ['爬取渠道', 'Crawl Channel']
        content_columns = ['文章内容', 'Article Content']
        publish_time_columns = ['文章发布时间', 'Publish Time']
        crawl_time_columns = ['爬取时间', 'Crawl Time']
        view_count_columns = ['浏览次数', 'View Count']
        likes_columns = ['点赞量', 'Likes']  # 新增字段
        comments_columns = ['评论量', 'Comments']  # 新增字段
        url_columns = ['文章链接', 'Article URL']
        id_columns = ['文章id', '文章ID', 'Article ID']

        records = []
        for index, row in df.iterrows():
            # 获取字段，使用默认值处理缺失的必填字段
            article_title = None
            for col in title_columns:
                if col in df.columns:
                    article_title = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            article_title = article_title or 'Unknown Title'
            if article_title == 'Unknown Title':
                logger.debug(f"第 {index + 2} 行文章标题缺失，使用默认值: {article_title}")

            unit_name = None
            for col in unit_columns:
                if col in df.columns:
                    unit_name = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            unit_name = unit_name or 'Unknown Unit'
            if unit_name == 'Unknown Unit':
                logger.debug(f"第 {index + 2} 行单位名称缺失，使用默认值: {unit_name}")

            crawl_channel = None
            for col in channel_columns:
                if col in df.columns:
                    crawl_channel = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            crawl_channel = crawl_channel or 'ExcelUpload'
            if crawl_channel == 'ExcelUpload':
                logger.debug(f"第 {index + 2} 行爬取渠道缺失，使用默认值: {crawl_channel}")

            # 处理 crawl_time
            crawl_time = None
            crawl_time_str = ''
            for col in crawl_time_columns:
                if col in df.columns:
                    crawl_time_str = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            if crawl_time_str:
                try:
                    crawl_time = datetime.strptime(crawl_time_str, '%Y-%m-%d-%H:%M')
                except ValueError:
                    try:
                        crawl_time = datetime.strptime(crawl_time_str, '%Y-%m-%d')
                    except ValueError:
                        logger.warning(f"第 {index + 2} 行爬取时间格式无效: {crawl_time_str}，使用当前时间")
                        crawl_time = datetime.now()
            else:
                logger.debug(f"第 {index + 2} 行未提供爬取时间，使用当前时间")
                crawl_time = datetime.now()

            # 处理 publish_time
            publish_time = None
            publish_time_str = ''
            for col in publish_time_columns:
                if col in df.columns:
                    publish_time_str = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            if publish_time_str:
                try:
                    publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d-%H:%M')
                except ValueError:
                    try:
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d')
                    except ValueError:
                        logger.warning(f"第 {index + 2} 行文章发布时间格式无效: {publish_time_str}")
                        publish_time = None

            # 处理 view_count
            view_count = None
            view_count_str = ''
            for col in view_count_columns:
                if col in df.columns:
                    view_count_str = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            if view_count_str and view_count_str.lower() != '无':
                try:
                    view_count = int(float(view_count_str))
                except ValueError:
                    logger.warning(f"第 {index + 2} 行浏览次数格式无效: {view_count_str}, 使用 0")
                    view_count = 0

            # 处理 likes
            likes = None
            likes_str = ''
            for col in likes_columns:
                if col in df.columns:
                    likes_str = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            if likes_str and likes_str.lower() != '无':
                try:
                    likes = int(float(likes_str))
                except ValueError:
                    logger.warning(f"第 {index + 2} 行点赞量格式无效: {likes_str}, 使用 0")
                    likes = 0

            # 处理 comments
            comments = None
            comments_str = ''
            for col in comments_columns:
                if col in df.columns:
                    comments_str = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else ''
                    break
            if comments_str and comments_str.lower() != '无':
                try:
                    comments = int(float(comments_str))
                except ValueError:
                    logger.warning(f"第 {index + 2} 行评论量格式无效: {comments_str}, 使用 0")
                    comments = 0

            # 获取其他字段
            article_content = None
            for col in content_columns:
                if col in df.columns:
                    article_content = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else None
                    break

            article_url = None
            for col in url_columns:
                if col in df.columns:
                    article_url = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else None
                    break

            article_id = None
            for col in id_columns:
                if col in df.columns:
                    article_id = str(row.get(col, '')).strip() if pd.notna(row.get(col)) else None
                    break
            if not article_id:
                logger.warning(f"第 {index + 2} 行文章ID缺失，跳过此记录")
                continue

            # 创建记录对象
            record = FxArticleRecords(
                crawl_time=crawl_time,
                crawl_channel=crawl_channel,
                unit_name=unit_name,
                article_title=article_title,
                article_content=article_content,
                publish_time=publish_time,
                view_count=view_count,
                likes=likes,  # 新增字段
                comments=comments,  # 新增字段
                article_url=article_url,
                article_id=article_id,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            records.append(record)
            logger.debug(f"已添加记录，article_id: {article_id}, 标题: {article_title}")

        if not records:
            logger.warning("没有有效的记录可以保存到数据库")
            raise ValueError("Excel 文件没有有效的记录（可能缺少必填字段或数据无效）")

        db.session.add_all(records)
        db.session.commit()
        logger.info(f"成功保存 {len(records)} 条记录到 fx_article_records 表")
        return len(records)  # 返回保存的记录数

    except Exception as e:
        logger.error(f"处理 Excel 文件失败: {str(e)}")
        db.session.rollback()
        raise


def upload_excel_file_1(file):
    logger = current_app.logger
    try:
        file.seek(0)  # 重置文件指针
        # 动态获取 Excel 的工作表名称
        try:
            xl = pd.ExcelFile(file, engine='openpyxl')
            if not xl.sheet_names:
                raise ValueError("Excel 文件不包含任何工作表")
            sheet_name = xl.sheet_names[0]  # 使用第一个工作表（Sheet1）
            logger.debug(f"可用工作表: {xl.sheet_names}, 选择的工作表: {sheet_name}")
        except Exception as e:
            logger.error(f"无法解析 Excel 文件: {str(e)}")
            raise ValueError(f"无法解析 Excel 文件: {str(e)}")

        # 读取 Excel 数据
        file.seek(0)  # 再次重置文件指针
        df = pd.read_excel(file, sheet_name=sheet_name, engine='openpyxl')
        # 清理列名（去除空格、BOM等）
        df.columns = [col.strip() if isinstance(col, str) else col for col in df.columns]
        logger.info(f"成功读取 Excel 文件，工作表: {sheet_name}, 列名: {list(df.columns)}")

        # 调试：打印前几行数据
        logger.debug(f"前5行数据:\n{df.head().to_dict(orient='records')}")

        # 支持多种可能的列名
        title_columns = ['文章标题', '标题', 'Title', 'Article Title']
        unit_columns = ['单位名称', 'Unit Name']
        channel_columns = ['爬取渠道', 'Crawl Channel']
        content_columns = ['文章内容', 'Article Content']
        publish_time_columns = ['文章发布时间', 'Publish Time']
        crawl_time_columns = ['爬取时间', 'Crawl Time']
        view_count_columns = ['浏览次数', 'View Count']
        url_columns = ['文章链接', 'Article URL']
        id_columns = ['文章id', '文章ID', 'Article ID']

        records = []
        for index, row in df.iterrows():
            # 获取字段，使用默认值处理缺失的必填字段
            article_title = None
            for col in title_columns:
                if col in df.columns:
                    article_title = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            article_title = article_title or 'Unknown Title'
            if article_title == 'Unknown Title':
                logger.debug(f"第 {index + 2} 行文章标题缺失，使用默认值: {article_title}")

            unit_name = None
            for col in unit_columns:
                if col in df.columns:
                    unit_name = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            unit_name = unit_name or 'Unknown Unit'
            if unit_name == 'Unknown Unit':
                logger.debug(f"第 {index + 2} 行单位名称缺失，使用默认值: {unit_name}")

            crawl_channel = None
            for col in channel_columns:
                if col in df.columns:
                    crawl_channel = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            crawl_channel = crawl_channel or 'ExcelUpload'
            if crawl_channel == 'ExcelUpload':
                logger.debug(f"第 {index + 2} 行爬取渠道缺失，使用默认值: {crawl_channel}")

            # 处理 crawl_time
            crawl_time = None
            crawl_time_str = ''
            for col in crawl_time_columns:
                if col in df.columns:
                    crawl_time_str = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            if crawl_time_str:
                try:
                    crawl_time = datetime.strptime(crawl_time_str, '%Y-%m-%d-%H:%M')
                except ValueError:
                    logger.warning(f"第 {index + 2} 行爬取时间格式无效: {crawl_time_str}，使用当前时间")
                    crawl_time = datetime.now()
            else:
                logger.debug(f"第 {index + 2} 行未提供爬取时间，使用当前时间")
                crawl_time = datetime.now()

            # 处理 publish_time
            publish_time = None
            publish_time_str = ''
            for col in publish_time_columns:
                if col in df.columns:
                    publish_time_str = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            if publish_time_str:
                try:
                    try:
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d-%H:%M')
                    except ValueError:
                        publish_time = datetime.strptime(publish_time_str, '%Y-%m-%d')
                except ValueError:
                    logger.warning(f"第 {index + 2} 行文章发布时间格式无效: {publish_time_str}")

            # 处理 view_count
            view_count = None
            view_count_str = ''
            for col in view_count_columns:
                if col in df.columns:
                    view_count_str = str(row.get(col, '')) if pd.notna(row.get(col)) else ''
                    break
            if view_count_str and view_count_str != '无':
                try:
                    view_count = int(float(view_count_str))
                except ValueError:
                    logger.warning(f"第 {index + 2} 行浏览次数格式无效: {view_count_str}")

            # 获取其他字段
            article_content = None
            for col in content_columns:
                if col in df.columns:
                    article_content = str(row.get(col, '')) if pd.notna(row.get(col)) else None
                    break

            article_url = None
            for col in url_columns:
                if col in df.columns:
                    article_url = str(row.get(col, '')) if pd.notna(row.get(col)) else None
                    break

            article_id = None
            for col in id_columns:
                if col in df.columns:
                    article_id = str(row.get(col, '')) if pd.notna(row.get(col)) else None
                    break

            record = FxArticleRecords(
                crawl_time=crawl_time,
                crawl_channel=crawl_channel,
                unit_name=unit_name,
                article_title=article_title,
                article_content=article_content,
                publish_time=publish_time,
                view_count=view_count,
                article_url=article_url,
                article_id=article_id,
                create_time=datetime.now(),
                update_time=datetime.now()
            )
            records.append(record)
            logger.debug(f"已添加记录，标题: {article_title}")

        if not records:
            logger.warning("没有有效的记录可以保存到数据库")
            raise ValueError("Excel 文件没有有效的记录（可能缺少必填字段或数据无效）")

        db.session.add_all(records)
        db.session.commit()
        logger.info(f"成功保存 {len(records)} 条记录到 fx_article_records 表")

    except Exception as e:
        logger.error(f"处理 Excel 文件失败: {str(e)}")
        db.session.rollback()
        raise


