# 普法工作管理系统统计接口
# 用于统计平台收集官方信息总数和今日增加数

from datetime import datetime, date, timedelta
from flask import request, jsonify
from flask_login import login_required
from sqlalchemy import func, and_
from web.routes import main_routes
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web.models.faxuan.FXAnalysusSummary import FxAnalysisSummary
from web import db


@main_routes.route('/api/faxuan/statistics/info_summary', methods=['GET'])
@login_required
def get_info_summary():
    """
    获取平台收集官方信息总数和今日增加数统计
    ---
    tags:
      - 普法统计
    description:
        获取平台收集官方信息总数和今日增加数的统计接口
    responses:
      200:
          description: 统计信息
          schema:
            type: object
            properties:
              code:
                type: integer
                description: 响应状态码
              msg:
                type: string
                description: 响应消息
              data:
                type: object
                properties:
                  total_count:
                    type: integer
                    description: 平台收集官方信息总数
                  today_count:
                    type: integer
                    description: 今日新增数量
                  today_date:
                    type: string
                    description: 统计日期
      500:
          description: 服务器错误
    """
    try:
        # 获取今天的日期
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        # 查询平台收集官方信息总数
        total_count = db.session.query(func.count(FxArticleRecords.id)).scalar() or 0
        
        # 查询今日新增数量（基于create_time字段）
        today_count = db.session.query(func.count(FxArticleRecords.id)).filter(
            and_(
                FxArticleRecords.create_time >= today_start,
                FxArticleRecords.create_time <= today_end
            )
        ).scalar() or 0
        
        # 构建响应数据
        result = {
            'total_count': total_count,
            'today_count': today_count,
            'today_date': today.strftime('%Y-%m-%d')
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/statistics/info_detail', methods=['GET'])
@login_required
def get_info_detail():
    """
    获取平台收集官方信息详细统计
    ---
    tags:
      - 普法统计
    description:
        获取平台收集官方信息的详细统计，包括按渠道、单位等维度的统计
    parameters:
      - name: date
        in: query
        type: string
        description: 查询日期，格式YYYY-MM-DD，不传则查询今日
      - name: channel
        in: query
        type: string
        description: 爬取渠道筛选
      - name: unit_name
        in: query
        type: string
        description: 单位名称筛选
    responses:
      200:
          description: 详细统计信息
      500:
          description: 服务器错误
    """
    try:
        # 获取查询参数
        query_date = request.args.get('date')
        channel = request.args.get('channel')
        unit_name = request.args.get('unit_name')
        
        # 处理日期参数
        if query_date:
            try:
                target_date = datetime.strptime(query_date, '%Y-%m-%d').date()
            except ValueError:
                return jsonify({
                    'code': 400,
                    'msg': '日期格式错误，请使用YYYY-MM-DD格式',
                    'data': None
                }), 400
        else:
            target_date = date.today()
        
        target_start = datetime.combine(target_date, datetime.min.time())
        target_end = datetime.combine(target_date, datetime.max.time())
        
        # 构建查询条件
        query_conditions = [
            FxArticleRecords.create_time >= target_start,
            FxArticleRecords.create_time <= target_end
        ]
        
        if channel:
            query_conditions.append(FxArticleRecords.crawl_channel.ilike(f'%{channel}%'))
        
        if unit_name:
            query_conditions.append(FxArticleRecords.unit_name.ilike(f'%{unit_name}%'))
        
        # 按渠道统计
        channel_stats = db.session.query(
            FxArticleRecords.crawl_channel,
            func.count(FxArticleRecords.id).label('count')
        ).filter(and_(*query_conditions)).group_by(
            FxArticleRecords.crawl_channel
        ).all()
        
        # 按单位统计
        unit_stats = db.session.query(
            FxArticleRecords.unit_name,
            func.count(FxArticleRecords.id).label('count')
        ).filter(and_(*query_conditions)).group_by(
            FxArticleRecords.unit_name
        ).order_by(func.count(FxArticleRecords.id).desc()).limit(10).all()
        
        # 总数统计
        total_count = db.session.query(func.count(FxArticleRecords.id)).filter(
            and_(*query_conditions)
        ).scalar() or 0
        
        # 构建响应数据
        result = {
            'query_date': target_date.strftime('%Y-%m-%d'),
            'total_count': total_count,
            'channel_stats': [
                {
                    'channel': stat.crawl_channel,
                    'count': stat.count
                } for stat in channel_stats
            ],
            'unit_stats': [
                {
                    'unit_name': stat.unit_name,
                    'count': stat.count
                } for stat in unit_stats
            ]
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/statistics/trend', methods=['GET'])
@login_required
def get_info_trend():
    """
    获取平台收集官方信息趋势统计
    ---
    tags:
      - 普法统计
    description:
        获取最近N天的信息收集趋势统计
    parameters:
      - name: days
        in: query
        type: integer
        description: 查询天数，默认7天
    responses:
      200:
          description: 趋势统计信息
      500:
          description: 服务器错误
    """
    try:
        # 获取查询天数参数
        days = int(request.args.get('days', 7))
        if days <= 0 or days > 365:
            days = 7
        
        # 计算日期范围
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        # 查询每日统计数据
        daily_stats = db.session.query(
            func.date(FxArticleRecords.create_time).label('date'),
            func.count(FxArticleRecords.id).label('count')
        ).filter(
            and_(
                func.date(FxArticleRecords.create_time) >= start_date,
                func.date(FxArticleRecords.create_time) <= end_date
            )
        ).group_by(
            func.date(FxArticleRecords.create_time)
        ).order_by(
            func.date(FxArticleRecords.create_time)
        ).all()
        
        # 构建完整的日期序列（包含0数据的日期）
        trend_data = []
        current_date = start_date
        stats_dict = {stat.date: stat.count for stat in daily_stats}
        
        while current_date <= end_date:
            trend_data.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'count': stats_dict.get(current_date, 0)
            })
            current_date += timedelta(days=1)
        
        # 计算总计和平均值
        total_count = sum(item['count'] for item in trend_data)
        avg_count = round(total_count / days, 2) if days > 0 else 0
        
        # 构建响应数据
        result = {
            'days': days,
            'start_date': start_date.strftime('%Y-%m-%d'),
            'end_date': end_date.strftime('%Y-%m-%d'),
            'total_count': total_count,
            'avg_count': avg_count,
            'trend_data': trend_data
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/statistics/unit_type_total', methods=['GET'])
@login_required
def get_unit_type_total_stats():
    """
    获取各类型行业系统发布总数统计
    ---
    tags:
      - 普法统计
    description:
        获取各类型行业系统发布总数统计，基于fx_analysis_summary表，用于前端图表展示
    responses:
      200:
          description: 各类型行业系统发布总数统计
      500:
          description: 服务器错误
    """
    try:
        # 查询分析汇总表所有数据
        summary_records = db.session.query(FxAnalysisSummary).all()

        if not summary_records:
            return jsonify({
                'code': 200,
                'msg': '操作成功',
                'data': {
                    'chart_data': [],
                    'x_axis_data': [],
                    'y_axis_data': [],
                    'summary_data': [],
                    'total_articles': 0
                }
            })

        # 按单位属性汇总数据
        unit_property_stats = {}
        summary_data = []

        for record in summary_records:
            unit_property = record.unit_property

            # 汇总单位属性数据
            if unit_property not in unit_property_stats:
                unit_property_stats[unit_property] = 0
            unit_property_stats[unit_property] += record.total_articles

            # 添加详细数据
            summary_data.append(record.to_json())

        # 定义标准的单位属性顺序（与前端保持一致）
        standard_properties = ['市级单位', '区级单位', '国企', '高校', '其他']

        # 构建图表数据，确保包含所有标准类型
        chart_data = []
        x_axis_data = []
        y_axis_data = []

        for unit_property in standard_properties:
            count = unit_property_stats.get(unit_property, 0)
            chart_data.append({
                'unit_property': unit_property,
                'total_articles': count
            })
            x_axis_data.append(unit_property)
            y_axis_data.append(count)

        # 按发布数量排序汇总数据
        summary_data.sort(key=lambda x: (x['unit_property'], -x['total_articles']))

        # 构建响应数据
        result = {
            'chart_data': chart_data,
            'x_axis_data': x_axis_data,  # 前端ECharts需要的x轴数据
            'y_axis_data': y_axis_data,  # 前端ECharts需要的y轴数据
            'summary_data': summary_data,  # 详细汇总数据
            'total_articles': sum(y_axis_data)  # 总发布数
        }

        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
