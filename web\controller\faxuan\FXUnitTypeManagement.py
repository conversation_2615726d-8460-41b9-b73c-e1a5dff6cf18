# 普法工作管理系统单位类型管理接口
# 用于管理单位类型映射

from datetime import datetime
from flask import request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, and_, or_
from web.routes import main_routes
from web.models.faxuan.FXUnitTypeMapping import FxUnitTypeMapping
from web import db


@main_routes.route('/api/faxuan/unit_type/list', methods=['GET'])
@login_required
def get_unit_type_list():
    """
    获取单位类型映射列表
    ---
    tags:
      - 单位类型管理
    description:
        获取单位类型映射列表，支持分页和搜索
    parameters:
      - name: page
        in: query
        type: integer
        description: 页码，默认1
      - name: size
        in: query
        type: integer
        description: 每页数量，默认10
      - name: unit_name
        in: query
        type: string
        description: 单位名称搜索
      - name: unit_type
        in: query
        type: string
        description: 单位类型筛选
    responses:
      200:
          description: 单位类型映射列表
      500:
          description: 服务器错误
    """
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        size = int(request.args.get('size', 10))
        unit_name = request.args.get('unit_name', '').strip()
        unit_type = request.args.get('unit_type', '').strip()
        
        # 构建查询条件
        query = FxUnitTypeMapping.query
        
        if unit_name:
            query = query.filter(FxUnitTypeMapping.unit_name.ilike(f'%{unit_name}%'))
        
        if unit_type:
            query = query.filter(FxUnitTypeMapping.unit_type == unit_type)
        
        # 分页查询
        pagination = query.order_by(FxUnitTypeMapping.create_time.desc()).paginate(
            page=page, per_page=size, error_out=False
        )
        
        # 构建响应数据
        result = {
            'total': pagination.total,
            'page': page,
            'size': size,
            'pages': pagination.pages,
            'list': [item.to_json() for item in pagination.items]
        }
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/unit_type/add', methods=['POST'])
@login_required
def add_unit_type():
    """
    添加单位类型映射
    ---
    tags:
      - 单位类型管理
    description:
        添加新的单位类型映射
    parameters:
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            unit_name:
              type: string
              description: 单位名称
            unit_type:
              type: string
              description: 单位类型
            unit_category:
              type: string
              description: 单位分类
    responses:
      200:
          description: 添加成功
      400:
          description: 参数错误
      500:
          description: 服务器错误
    """
    try:
        data = request.get_json()
        
        # 验证必填参数
        if not data or not data.get('unit_name') or not data.get('unit_type'):
            return jsonify({
                'code': 400,
                'msg': '单位名称和单位类型不能为空',
                'data': None
            }), 400
        
        unit_name = data.get('unit_name').strip()
        unit_type = data.get('unit_type').strip()
        unit_category = data.get('unit_category', '').strip()
        
        # 检查是否已存在
        existing = FxUnitTypeMapping.query.filter_by(unit_name=unit_name).first()
        if existing:
            return jsonify({
                'code': 400,
                'msg': '该单位名称已存在',
                'data': None
            }), 400
        
        # 创建新记录
        mapping = FxUnitTypeMapping(
            unit_name=unit_name,
            unit_type=unit_type,
            unit_category=unit_category if unit_category else None,
            create_by=current_user.username if hasattr(current_user, 'username') else 'admin'
        )
        
        db.session.add(mapping)
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'msg': '添加成功',
            'data': mapping.to_json()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/unit_type/update/<int:mapping_id>', methods=['PUT'])
@login_required
def update_unit_type(mapping_id):
    """
    更新单位类型映射
    ---
    tags:
      - 单位类型管理
    description:
        更新单位类型映射
    parameters:
      - name: mapping_id
        in: path
        type: integer
        required: true
        description: 映射ID
      - name: body
        in: body
        required: true
        schema:
          type: object
          properties:
            unit_name:
              type: string
              description: 单位名称
            unit_type:
              type: string
              description: 单位类型
            unit_category:
              type: string
              description: 单位分类
            is_active:
              type: boolean
              description: 是否启用
    responses:
      200:
          description: 更新成功
      400:
          description: 参数错误
      404:
          description: 记录不存在
      500:
          description: 服务器错误
    """
    try:
        # 查找记录
        mapping = FxUnitTypeMapping.query.get(mapping_id)
        if not mapping:
            return jsonify({
                'code': 404,
                'msg': '记录不存在',
                'data': None
            }), 404
        
        data = request.get_json()
        if not data:
            return jsonify({
                'code': 400,
                'msg': '请求数据不能为空',
                'data': None
            }), 400
        
        # 更新字段
        if 'unit_name' in data:
            unit_name = data['unit_name'].strip()
            if not unit_name:
                return jsonify({
                    'code': 400,
                    'msg': '单位名称不能为空',
                    'data': None
                }), 400
            
            # 检查名称是否重复（排除自己）
            existing = FxUnitTypeMapping.query.filter(
                and_(
                    FxUnitTypeMapping.unit_name == unit_name,
                    FxUnitTypeMapping.id != mapping_id
                )
            ).first()
            if existing:
                return jsonify({
                    'code': 400,
                    'msg': '该单位名称已存在',
                    'data': None
                }), 400
            
            mapping.unit_name = unit_name
        
        if 'unit_type' in data:
            unit_type = data['unit_type'].strip()
            if not unit_type:
                return jsonify({
                    'code': 400,
                    'msg': '单位类型不能为空',
                    'data': None
                }), 400
            mapping.unit_type = unit_type
        
        if 'unit_category' in data:
            mapping.unit_category = data['unit_category'].strip() if data['unit_category'] else None
        
        if 'is_active' in data:
            mapping.is_active = bool(data['is_active'])
        
        mapping.update_by = current_user.username if hasattr(current_user, 'username') else 'admin'
        
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'msg': '更新成功',
            'data': mapping.to_json()
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/unit_type/delete/<int:mapping_id>', methods=['DELETE'])
@login_required
def delete_unit_type(mapping_id):
    """
    删除单位类型映射
    ---
    tags:
      - 单位类型管理
    description:
        删除单位类型映射
    parameters:
      - name: mapping_id
        in: path
        type: integer
        required: true
        description: 映射ID
    responses:
      200:
          description: 删除成功
      404:
          description: 记录不存在
      500:
          description: 服务器错误
    """
    try:
        # 查找记录
        mapping = FxUnitTypeMapping.query.get(mapping_id)
        if not mapping:
            return jsonify({
                'code': 404,
                'msg': '记录不存在',
                'data': None
            }), 404
        
        db.session.delete(mapping)
        db.session.commit()
        
        return jsonify({
            'code': 200,
            'msg': '删除成功',
            'data': None
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500


@main_routes.route('/api/faxuan/unit_type/types', methods=['GET'])
@login_required
def get_unit_types():
    """
    获取所有单位类型选项
    ---
    tags:
      - 单位类型管理
    description:
        获取所有可用的单位类型选项
    responses:
      200:
          description: 单位类型选项列表
      500:
          description: 服务器错误
    """
    try:
        # 获取所有不同的单位类型
        types = db.session.query(FxUnitTypeMapping.unit_type).distinct().all()
        
        # 添加默认类型
        default_types = ['市级单位', '区级单位', '高校', '国企', '其他']
        all_types = set(default_types)
        
        for type_tuple in types:
            all_types.add(type_tuple[0])
        
        result = sorted(list(all_types))
        
        return jsonify({
            'code': 200,
            'msg': '操作成功',
            'data': result
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'服务器错误: {str(e)}',
            'data': None
        }), 500
