# 分析助手_文章对应标签的dify

# -*- coding: utf-8 -*-
import requests
import json
from datetime import datetime



# 调用dify工作流
def call_dify_workflow(wenzhang_id, danweimingcheng, wenzhang_biaoti, wenzhang_neirong):
    """
    Call the Dify workflow to process a legal education article.

    Args:
        wenzhang_id (str): ID of the article.
        danweimingcheng (str): Name of the unit/organization.
        wenzhang_biaoti (str): Title of the article.
        wenzhang_neirong (str): Content of the article.

    Returns:
        dict: Response from the Dify API or error message.
    """
    # 硬编码配置
    api_key = "app-t2V25A9lon5mdR51UlSVBxsa"
    workflow_id = "8c3e3cd0-9fc8-4e7c-8082-d1f837fe92ac"
    endpoint = "http://192.168.13.34:8082/v1/workflows/run"

    # 设置当前时间戳
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M PDT")

    # 设置请求头
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json;charset=UTF-8",
        "Accept-Charset": "UTF-8"
    }

    # 构造请求 payload，适配 YAML 工作流的输入字段
    payload = {
        "inputs": {
            "wenzhang_id": wenzhang_id,
            "danweimingcheng": danweimingcheng,
            "wenzhang_biaoti": wenzhang_biaoti,
            "wenzhang_neirong": wenzhang_neirong
        },
        "response_mode": "streaming",
        "user": "abc-123",
        "workflow_id": workflow_id,
        "context": {"timestamp": current_time}
    }

    # 打印请求信息以便调试
    print("发送dify接口数据成功")

    # 存储最终结果
    final_outputs = None

    try:
        # 发送 POST 请求调用工作流
        response = requests.post(
            endpoint,
            headers=headers,
            json=payload,
            stream=True,
            timeout=30
        )
        print(f"请求成功，状态码: {response.status_code}")

        # 检查响应状态
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            print(f"错误信息: {response.text}")
            return {"status": "error", "result": response.text}

        # 处理流式响应
        print("开始接收流式响应...")
        for line in response.iter_lines():
            if line:
                decoded_line = line.decode('utf-8').strip()
                if decoded_line.startswith('data:'):
                    decoded_line = decoded_line[5:].strip()

                try:
                    if decoded_line:
                        data = json.loads(decoded_line)
                        # 打印所有事件以便调试
                        # print("收到事件:", json.dumps(data, ensure_ascii=False, indent=2))

                        # 检查是否为 workflow_finished 事件
                        if data.get("event") == "workflow_finished":

                            final_outputs = data.get("data", {}).get("outputs", {})
                            print("工作流完成，输出:", json.dumps(final_outputs, ensure_ascii=False, indent=2))


                        elif data.get("event") == "error":
                            print("工作流错误:", data.get("error", "未知错误"))
                            return {"status": "error", "result": data.get("error", "未知错误")}

                except json.JSONDecodeError as e:
                    print("收到非 JSON 数据:", decoded_line)

        # 返回最终结果
        if final_outputs:
            if "output" in final_outputs:
                return {"status": "success", "result": final_outputs["output"]}
            return {"status": "success", "result": final_outputs}
        return {"status": "partial", "result": "无最终输出"}

    except requests.exceptions.HTTPError as e:
        error_info = {
            "error": f"HTTP Error: {str(e)}",
            "status_code": e.response.status_code if e.response else None,
            "response_text": e.response.text if e.response else "No response text",
            "headers": e.response.headers if e.response else None
        }
        print(json.dumps(error_info, ensure_ascii=False, indent=2))
        return {"status": "error", "result": error_info}
    except requests.exceptions.ConnectionError as e:
        print(f"连接错误: {str(e)}")
        return {"status": "error", "result": f"连接错误: {str(e)}"}
    except requests.exceptions.Timeout as e:
        print(f"超时错误: {str(e)}")
        return {"status": "error", "result": f"超时错误: {str(e)}"}
    except requests.exceptions.RequestException as e:
        print(f"通用请求错误: {str(e)}")
        return {"status": "error", "result": f"通用请求错误: {str(e)}"}
    except Exception as e:
        print(f"未知错误: {str(e)}")
        return {"status": "error", "result": f"未知错误: {str(e)}"}