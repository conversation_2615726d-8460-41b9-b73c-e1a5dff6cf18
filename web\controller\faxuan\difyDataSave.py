# 文章对应标签dify的结果保存
from datetime import datetime
from web import db
from web.models.faxuan.FXEducationArticles import LegalEducationArticle

# dify工作流运行结果的保存
def dify_analysis_save(dify_result):
    """
    将 Dify 分析结果保存到数据库。

    Args:
        dify_result (dict): Dify 工作流返回的结果，包含 'status' 和 'result' 字段。

    Returns:
        dict: 保存结果，包含状态和消息。
    """
    try:
        # 验证结果状态
        if dify_result.get('status') != 'success':
            return {"status": "error", "message": "Dify analysis failed"}

        # 获取 text 数据
        result_data = dify_result.get('result', {})
        text = result_data.get('text')
        if not text:
            return {"status": "error", "message": "No text data in result"}

        # 使用 && 切分数据
        fields = text.split('&&')
        # 移除末尾的 @@（如果存在）
        if fields[-1] == '@@':
            fields = fields[:-1]

        # 验证字段数量
        if len(fields) != 9:
            return {"status": "error", "message": f"Invalid number of fields: expected 9, got {len(fields)}"}

        # 映射切分数据到字段
        type_class, article_id, unit_name, unit_property, industry_system, unit_district, legal_content_type, target_group, people_scale = fields

        # 生成 month 字段（基于当前日期）
        current_month = datetime.now().strftime("%Y-%m")

        # 创建新记录
        new_article = LegalEducationArticle(
            article_id=article_id,
            unit_name=unit_name,
            unit_property=unit_property,
            industry_system=industry_system,
            unit_district=unit_district,
            legal_content_type=legal_content_type,
            target_group=target_group,
            people_scale=people_scale,
            month=current_month,
            type_class=type_class  # 1 表示普法，0 表示非普法
        )

        # 添加到数据库会话并提交
        db.session.add(new_article)
        db.session.commit()

        return {"status": "success", "message": f"Article {article_id} saved successfully"}

    except Exception as e:
        # 回滚事务并返回错误
        db.session.rollback()
        return {"status": "error", "message": f"Failed to save to database: {str(e)}"}
