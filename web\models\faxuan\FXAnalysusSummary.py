from datetime import datetime

from web import db

class AnalysisSummary(db.Model):
    __tablename__ = 'fX_analysis_summary'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    unit_property = db.Column(db.String(50), nullable=False, comment='单位属性（市级政府、区级政府、高校、市属国企）')
    industry_system = db.Column(db.String(100), nullable=False, comment='行业系统（纪委监委系统、教育系统等）')
    total_articles = db.Column(db.Integer, nullable=False, comment='发布文章总数')
    total_views = db.Column(db.BigInteger, nullable=False, comment='总阅读量')
    max_article_views = db.Column(db.Integer, nullable=False, comment='单篇文章最高阅读量')
    month = db.Column(db.CHAR(7), nullable=False, comment='月份（格式：YYYY-MM）')
    total_likes = db.Column(db.BigInteger, nullable=False, comment='总点赞量')
    max_article_likes = db.Column(db.Integer, nullable=False, comment='单篇文章最高点赞量')
    total_comments = db.Column(db.BigInteger, nullable=False, comment='总评论量')
    max_article_comments = db.Column(db.Integer, nullable=False, comment='单篇文章最高评论量')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')
