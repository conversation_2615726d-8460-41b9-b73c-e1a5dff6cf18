# models.py (示例模型)
from datetime import datetime

from web import db
class LegalEducationArticle(db.Model):
    __tablename__ = 'fx_education_articles'

    id = db.Column(db.BigInteger, primary_key=True, autoincrement=True, comment='自增主键')
    article_id = db.Column(db.String(20), nullable=False, comment='文章ID')
    unit_name = db.Column(db.String(100), nullable=False, comment='单位名称')
    unit_property = db.Column(db.String(50), nullable=False, comment='单位属性')
    industry_system = db.Column(db.String(50), nullable=False, comment='行业系统')
    unit_district = db.Column(db.String(50), nullable=False, comment='单位所在区')
    legal_content_type = db.Column(db.String(50), nullable=False, comment='普法内容类型')
    target_group = db.Column(db.String(50), nullable=False, comment='受众群体')
    people_scale = db.Column(db.String(20), nullable=False, comment='人数规模')
    month = db.Column(db.String(7), nullable=False, comment='月份（YYYY-MM）')
    type_class = db.Column(db.String(50), nullable=True, comment='是否为普法，0为非普法，1为普法')
    create_time = db.Column(db.DateTime, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')