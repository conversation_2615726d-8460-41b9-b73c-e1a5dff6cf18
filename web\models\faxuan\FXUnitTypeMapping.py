from datetime import datetime
from web import db

class FxUnitTypeMapping(db.Model):
    """单位类型映射表"""
    __tablename__ = 'fx_unit_type_mapping'

    id = db.Column(db.<PERSON>I<PERSON>ger, primary_key=True, autoincrement=True, comment='自增主键')
    unit_name = db.Column(db.String(100), nullable=False, unique=True, comment='单位名称')
    unit_type = db.Column(db.String(50), nullable=False, comment='单位类型（市级单位、区级单位、高校、国企）')
    unit_category = db.Column(db.String(100), nullable=True, comment='单位分类（法院系统、教育系统等）')
    is_active = db.Column(db.<PERSON>, nullable=False, default=True, comment='是否启用')
    create_time = db.Column(db.DateTime, nullable=False, default=datetime.now, comment='记录创建时间')
    update_time = db.Column(db.DateTime, onupdate=datetime.now, comment='记录更新时间')
    create_by = db.Column(db.String(100), comment='创建人')
    update_by = db.Column(db.String(100), comment='更新人')

    def to_json(self):
        """转换为JSON格式"""
        return {
            'id': self.id,
            'unit_name': self.unit_name,
            'unit_type': self.unit_type,
            'unit_category': self.unit_category,
            'is_active': self.is_active,
            'create_time': self.create_time.strftime('%Y-%m-%d %H:%M:%S') if self.create_time else None,
            'update_time': self.update_time.strftime('%Y-%m-%d %H:%M:%S') if self.update_time else None,
            'create_by': self.create_by,
            'update_by': self.update_by
        }

    @staticmethod
    def get_unit_type_by_name(unit_name):
        """根据单位名称获取单位类型"""
        mapping = FxUnitTypeMapping.query.filter_by(unit_name=unit_name, is_active=True).first()
        if mapping:
            return mapping.unit_type
        
        # 如果没有找到映射，使用默认规则推断
        return FxUnitTypeMapping.infer_unit_type(unit_name)
    
    @staticmethod
    def infer_unit_type(unit_name):
        """根据单位名称推断单位类型"""
        unit_name_lower = unit_name.lower()
        
        # 市级单位
        if any(keyword in unit_name_lower for keyword in ['市法院', '市教育局', '市政府', '市委', '市人大', '市政协', '市检察院']):
            return '市级单位'
        
        # 区级单位  
        if any(keyword in unit_name_lower for keyword in ['区司法局', '区政府', '区委', '区人大', '区政协', '区检察院', '区法院']):
            return '区级单位'
        
        # 高校
        if any(keyword in unit_name_lower for keyword in ['大学', '学院', '高校']):
            return '高校'
        
        # 国企
        if any(keyword in unit_name_lower for keyword in ['集团', '公司', '国有', '国企', '控股']):
            return '国企'
        
        # 默认归类为其他
        return '其他'

    @staticmethod
    def init_default_mappings():
        """初始化默认的单位类型映射"""
        default_mappings = [
            {'unit_name': '市法院', 'unit_type': '市级单位', 'unit_category': '法院系统'},
            {'unit_name': '市教育局', 'unit_type': '市级单位', 'unit_category': '教育系统'},
            {'unit_name': '玄武区司法局', 'unit_type': '区级单位', 'unit_category': '司法系统'},
            {'unit_name': '秦淮区司法局', 'unit_type': '区级单位', 'unit_category': '司法系统'},
            {'unit_name': '栖霞区司法局', 'unit_type': '区级单位', 'unit_category': '司法系统'},
            {'unit_name': '雨花台区司法局', 'unit_type': '区级单位', 'unit_category': '司法系统'},
            {'unit_name': '南京大学', 'unit_type': '高校', 'unit_category': '高校系统'},
            {'unit_name': '东南大学', 'unit_type': '高校', 'unit_category': '高校系统'},
            {'unit_name': '南京市河西新城区国有资产经营控股(集团)有限责任公司', 'unit_type': '国企', 'unit_category': '国企系统'},
            {'unit_name': '中建安装集团有限公司', 'unit_type': '国企', 'unit_category': '国企系统'},
        ]
        
        for mapping_data in default_mappings:
            existing = FxUnitTypeMapping.query.filter_by(unit_name=mapping_data['unit_name']).first()
            if not existing:
                mapping = FxUnitTypeMapping(
                    unit_name=mapping_data['unit_name'],
                    unit_type=mapping_data['unit_type'],
                    unit_category=mapping_data['unit_category'],
                    create_by='system'
                )
                db.session.add(mapping)
        
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e
