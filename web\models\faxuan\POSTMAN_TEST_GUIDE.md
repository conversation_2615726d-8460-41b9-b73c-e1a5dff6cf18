# Postman 接口测试指南

## 1. 导入测试集合

### 方法一：导入JSON文件
1. 打开Postman
2. 点击左上角 "Import" 按钮
3. 选择 `postman_collection.json` 文件导入
4. 导入后会看到 "普法工作管理系统 - 单位类型统计接口" 集合

### 方法二：手动创建请求
按照下面的步骤手动创建测试请求

## 2. 环境变量设置

在Postman中设置环境变量：
- 变量名：`base_url`
- 变量值：`http://localhost:5000` (根据您的服务器地址调整)

## 3. 认证处理

### 步骤1：先登录获取认证
**请求配置：**
```
方法: POST
URL: {{base_url}}/login
Headers: 
  Content-Type: application/json
Body (raw JSON):
{
  "username": "admin",
  "password": "admin123"
}
```

**注意：** 登录成功后，Postman会自动保存Cookie，后续请求会自动携带认证信息。

### 步骤2：测试主要接口

## 4. 核心接口测试

### 4.1 获取今日发布数统计

**请求配置：**
```
方法: GET
URL: {{base_url}}/api/faxuan/statistics/unit_type_today
Headers: 
  Content-Type: application/json
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "query_date": "2024-01-15",
    "chart_data": [
      {"unit_type": "市级单位", "count": 5},
      {"unit_type": "区级单位", "count": 8},
      {"unit_type": "国企", "count": 3},
      {"unit_type": "高校", "count": 2},
      {"unit_type": "其他", "count": 0}
    ],
    "x_axis_data": ["市级单位", "区级单位", "国企", "高校", "其他"],
    "y_axis_data": [5, 8, 3, 2, 0],
    "detail_data": [...],
    "total_count": 18
  }
}
```

### 4.2 获取指定日期统计

**请求配置：**
```
方法: GET
URL: {{base_url}}/api/faxuan/statistics/unit_type_today?date=2024-01-15
```

### 4.3 获取单位类型映射列表

**请求配置：**
```
方法: GET
URL: {{base_url}}/api/faxuan/unit_type/list?page=1&size=10
```

## 5. 测试场景

### 场景1：正常流程测试
1. 先调用登录接口
2. 调用今日统计接口
3. 验证返回数据格式
4. 检查图表数据是否正确

### 场景2：参数测试
1. 测试不同日期参数
2. 测试无效日期格式
3. 测试空参数

### 场景3：错误处理测试
1. 测试未登录访问
2. 测试无效日期格式
3. 测试服务器错误情况

## 6. 常见问题解决

### 问题1：401 Unauthorized
**原因：** 未登录或登录过期
**解决：** 先调用登录接口获取认证

### 问题2：404 Not Found
**原因：** 接口路径错误或服务未启动
**解决：** 检查URL路径和服务状态

### 问题3：500 Internal Server Error
**原因：** 服务器内部错误
**解决：** 检查服务器日志，可能是数据库连接或代码错误

### 问题4：数据为空
**原因：** 数据库中没有对应日期的数据
**解决：** 
1. 检查数据库中是否有测试数据
2. 运行测试脚本创建测试数据：
   ```bash
   python web/models/faxuan/test_unit_type_stats.py
   ```

## 7. 测试脚本

### 在Postman中添加测试脚本

在请求的 "Tests" 标签页中添加以下脚本：

```javascript
// 基本状态检查
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// 响应结构检查
pm.test("Response has correct structure", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData).to.have.property('code');
    pm.expect(jsonData).to.have.property('msg');
    pm.expect(jsonData).to.have.property('data');
});

// 成功响应检查
pm.test("Response is successful", function () {
    var jsonData = pm.response.json();
    pm.expect(jsonData.code).to.eql(200);
    pm.expect(jsonData.msg).to.eql('操作成功');
});

// 数据格式检查（仅针对统计接口）
pm.test("Statistics data format is correct", function () {
    var jsonData = pm.response.json();
    if (jsonData.code === 200 && jsonData.data) {
        pm.expect(jsonData.data).to.have.property('query_date');
        pm.expect(jsonData.data).to.have.property('chart_data');
        pm.expect(jsonData.data).to.have.property('x_axis_data');
        pm.expect(jsonData.data).to.have.property('y_axis_data');
        pm.expect(jsonData.data).to.have.property('total_count');
        
        // 检查数组长度一致性
        pm.expect(jsonData.data.x_axis_data.length).to.eql(jsonData.data.y_axis_data.length);
    }
});
```

## 8. 快速测试步骤

1. **启动服务器**
   ```bash
   python app.py  # 或您的启动命令
   ```

2. **导入Postman集合**
   - 导入 `postman_collection.json`

3. **设置环境变量**
   - `base_url`: `http://localhost:5000`

4. **按顺序执行请求**
   - 先执行登录请求
   - 再执行统计接口请求

5. **检查响应**
   - 状态码应为 200
   - 响应格式应符合预期
   - 数据内容应合理

## 9. 调试技巧

1. **查看请求详情**
   - 在Postman的Console中查看完整请求和响应

2. **检查Cookie**
   - 在Headers中查看是否携带了正确的Cookie

3. **查看服务器日志**
   - 检查Flask应用的控制台输出

4. **使用Postman的代码生成功能**
   - 可以生成curl命令或其他语言的代码

## 10. 示例curl命令

如果您更喜欢使用curl测试：

```bash
# 登录
curl -X POST http://localhost:5000/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}' \
  -c cookies.txt

# 获取统计数据
curl -X GET http://localhost:5000/api/faxuan/statistics/unit_type_today \
  -H "Content-Type: application/json" \
  -b cookies.txt

# 获取指定日期统计
curl -X GET "http://localhost:5000/api/faxuan/statistics/unit_type_today?date=2024-01-15" \
  -H "Content-Type: application/json" \
  -b cookies.txt
```
