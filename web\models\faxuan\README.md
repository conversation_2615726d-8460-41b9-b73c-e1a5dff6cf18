# 普法工作管理系统统计接口说明

## 概述

本模块为普法工作管理系统提供统计功能，主要用于统计平台收集官方信息的总数和今日增加数等相关数据。

## 数据库表结构

### fx_article_records (文章记录表)
- 存储平台收集的所有官方信息文章
- 主要字段：文章标题、内容、发布时间、浏览量、点赞量、评论量等
- 统计接口直接基于此表进行实时查询

## API接口

### 1. 获取信息汇总统计
**接口地址：** `GET /api/faxuan/statistics/info_summary`

**功能描述：** 获取平台收集官方信息总数和今日增加数

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "total_count": 1250,
        "today_count": 25,
        "today_date": "2025-08-05"
    }
}
```

### 2. 获取信息详细统计
**接口地址：** `GET /api/faxuan/statistics/info_detail`

**功能描述：** 获取平台收集官方信息的详细统计，支持按渠道、单位等维度筛选

**请求参数：**
- `date` (可选): 查询日期，格式YYYY-MM-DD，不传则查询今日
- `channel` (可选): 爬取渠道筛选
- `unit_name` (可选): 单位名称筛选

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "query_date": "2025-08-05",
        "total_count": 25,
        "channel_stats": [
            {
                "channel": "微信公众号",
                "count": 15
            },
            {
                "channel": "官网",
                "count": 10
            }
        ],
        "unit_stats": [
            {
                "unit_name": "市法院",
                "count": 8
            },
            {
                "unit_name": "市教育局",
                "count": 7
            }
        ]
    }
}
```

### 3. 获取信息趋势统计
**接口地址：** `GET /api/faxuan/statistics/trend`

**功能描述：** 获取最近N天的信息收集趋势统计

**请求参数：**
- `days` (可选): 查询天数，默认7天，最大365天

**响应示例：**
```json
{
    "code": 200,
    "msg": "操作成功",
    "data": {
        "days": 7,
        "start_date": "2025-07-30",
        "end_date": "2025-08-05",
        "total_count": 175,
        "avg_count": 25.0,
        "trend_data": [
            {
                "date": "2025-07-30",
                "count": 20
            },
            {
                "date": "2025-07-31",
                "count": 25
            },
            {
                "date": "2025-08-01",
                "count": 30
            },
            {
                "date": "2025-08-02",
                "count": 22
            },
            {
                "date": "2025-08-03",
                "count": 28
            },
            {
                "date": "2025-08-04",
                "count": 25
            },
            {
                "date": "2025-08-05",
                "count": 25
            }
        ]
    }
}
```

## 数据使用说明

统计接口直接基于 `fx_article_records` 表进行实时查询，无需额外的数据库表或定时任务。

## 注意事项

1. 所有接口都需要登录认证（`@login_required`装饰器）
2. 统计数据基于`fx_article_records`表的`create_time`字段进行实时查询
3. 日期格式统一使用`YYYY-MM-DD`
4. 所有数值字段在数据库中为NULL时，接口返回0

## 错误处理

接口统一返回格式：
- 成功：`{"code": 200, "msg": "操作成功", "data": {...}}`
- 失败：`{"code": 500, "msg": "错误信息", "data": null}`

常见错误码：
- 400：请求参数错误
- 401：未登录或认证失败
- 500：服务器内部错误
