# 普法工作管理系统 - 各类型单位今日发布数图表接口

## 功能概述

本功能为普法工作管理系统提供各类型单位今日发布数的统计接口，支持前端图表展示。

## 文件说明

### 数据库模型文件

1. **FXUnitTypeMapping.py** - 单位类型映射模型
   - 管理单位名称与单位类型的映射关系
   - 支持自动推断和手动配置单位类型
   - 提供默认映射初始化功能

2. **FXArticleRecords.py** - 文章记录模型（已更新）
   - 新增 `get_unit_type()` 方法，根据单位名称获取单位类型
   - 集成单位类型映射功能

### 接口文件

1. **FXStatistics.py** - 统计接口（已更新）
   - 新增 `/api/faxuan/statistics/unit_type_today` 接口
   - 提供各类型单位今日发布数统计
   - 返回适合ECharts图表的数据格式

2. **FXUnitTypeManagement.py** - 单位类型管理接口
   - 提供单位类型映射的CRUD操作
   - 支持分页查询和搜索功能
   - 管理员可以维护单位类型映射关系

### 数据库脚本

1. **init_unit_type_mapping.sql** - 数据库初始化脚本
   - 创建单位类型映射表
   - 插入默认的单位类型映射数据

### 测试文件

1. **test_unit_type_stats.py** - 功能测试脚本
   - 测试单位类型映射功能
   - 创建测试数据
   - 验证统计接口逻辑

## 接口说明

### 1. 各类型单位今日发布数统计接口

**接口地址：** `GET /api/faxuan/statistics/unit_type_today`

**请求参数：**
- `date` (可选): 查询日期，格式YYYY-MM-DD，不传则查询今日

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "query_date": "2024-01-15",
    "chart_data": [
      {"unit_type": "市级单位", "count": 5},
      {"unit_type": "区级单位", "count": 8},
      {"unit_type": "国企", "count": 3},
      {"unit_type": "高校", "count": 2},
      {"unit_type": "其他", "count": 0}
    ],
    "x_axis_data": ["市级单位", "区级单位", "国企", "高校", "其他"],
    "y_axis_data": [5, 8, 3, 2, 0],
    "detail_data": [
      {
        "unit_type": "市级单位",
        "unit_name": "市法院",
        "count": 3,
        "last_publish_time": "2024-01-15 14:30:00"
      }
    ],
    "total_count": 18
  }
}
```

### 2. 单位类型管理接口

#### 获取单位类型映射列表
**接口地址：** `GET /api/faxuan/unit_type/list`

**请求参数：**
- `page`: 页码，默认1
- `size`: 每页数量，默认10
- `unit_name`: 单位名称搜索
- `unit_type`: 单位类型筛选

#### 添加单位类型映射
**接口地址：** `POST /api/faxuan/unit_type/add`

**请求体：**
```json
{
  "unit_name": "新单位名称",
  "unit_type": "市级单位",
  "unit_category": "法院系统"
}
```

#### 更新单位类型映射
**接口地址：** `PUT /api/faxuan/unit_type/update/{mapping_id}`

#### 删除单位类型映射
**接口地址：** `DELETE /api/faxuan/unit_type/delete/{mapping_id}`

#### 获取单位类型选项
**接口地址：** `GET /api/faxuan/unit_type/types`

## 单位类型分类规则

系统支持以下单位类型：

1. **市级单位** - 市法院、市教育局、市政府等市级机关
2. **区级单位** - 各区司法局、区政府等区级机关
3. **高校** - 各大学、学院等教育机构
4. **国企** - 各类国有企业、集团公司
5. **其他** - 不属于以上类型的其他单位

## 部署说明

### 1. 数据库初始化

执行数据库脚本创建单位类型映射表：
```sql
-- 执行 init_unit_type_mapping.sql 脚本
```

### 2. 初始化默认映射

在Python环境中执行：
```python
from web.models.faxuan.FXUnitTypeMapping import FxUnitTypeMapping
from web import create_app, db

app = create_app()
with app.app_context():
    FxUnitTypeMapping.init_default_mappings()
```

### 3. 测试功能

运行测试脚本验证功能：
```bash
python web/models/faxuan/test_unit_type_stats.py
```

## 前端集成

前端可以直接调用统计接口获取图表数据：

```javascript
// 获取今日发布数统计
fetch('/api/faxuan/statistics/unit_type_today')
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      const chartData = data.data;
      
      // 用于ECharts柱状图
      const option = {
        xAxis: {
          data: chartData.x_axis_data
        },
        yAxis: {},
        series: [{
          type: 'bar',
          data: chartData.y_axis_data
        }]
      };
      
      // 设置图表选项
      chart.setOption(option);
    }
  });
```

## 注意事项

1. 接口需要登录认证，请确保用户已登录
2. 单位类型映射支持动态配置，管理员可以通过管理接口维护
3. 统计数据基于文章记录的创建时间，而非发布时间
4. 系统会自动推断未配置映射的单位类型
5. 建议定期维护单位类型映射表，确保分类准确性
