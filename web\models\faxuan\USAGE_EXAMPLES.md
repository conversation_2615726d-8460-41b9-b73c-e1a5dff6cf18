# 普法工作管理系统统计接口使用示例

## 前端调用示例

### 1. JavaScript/Ajax 调用示例

```javascript
// 获取信息汇总统计
function getInfoSummary() {
    $.ajax({
        url: '/api/faxuan/statistics/info_summary',
        type: 'GET',
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        success: function(response) {
            if (response.code === 200) {
                const data = response.data;
                $('#total-count').text(data.total_count);
                $('#today-count').text(data.today_count);
                $('#today-date').text(data.today_date);
            } else {
                console.error('获取统计数据失败:', response.msg);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}

// 获取信息详细统计
function getInfoDetail(date, channel, unitName) {
    const params = {};
    if (date) params.date = date;
    if (channel) params.channel = channel;
    if (unitName) params.unit_name = unitName;
    
    $.ajax({
        url: '/api/faxuan/statistics/info_detail',
        type: 'GET',
        data: params,
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        success: function(response) {
            if (response.code === 200) {
                const data = response.data;
                
                // 更新渠道统计图表
                updateChannelChart(data.channel_stats);
                
                // 更新单位统计表格
                updateUnitTable(data.unit_stats);
                
                // 更新总数显示
                $('#detail-total-count').text(data.total_count);
            } else {
                console.error('获取详细统计失败:', response.msg);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}

// 获取趋势统计
function getInfoTrend(days = 7) {
    $.ajax({
        url: '/api/faxuan/statistics/trend',
        type: 'GET',
        data: { days: days },
        headers: {
            'Authorization': 'Bearer ' + localStorage.getItem('token')
        },
        success: function(response) {
            if (response.code === 200) {
                const data = response.data;
                
                // 更新趋势图表
                updateTrendChart(data.trend_data);
                
                // 更新统计信息
                $('#trend-total').text(data.total_count);
                $('#trend-avg').text(data.avg_count);
            } else {
                console.error('获取趋势统计失败:', response.msg);
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', error);
        }
    });
}

// 更新渠道统计图表（使用ECharts）
function updateChannelChart(channelStats) {
    const chartDom = document.getElementById('channel-chart');
    const myChart = echarts.init(chartDom);
    
    const option = {
        title: {
            text: '渠道统计'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [{
            name: '文章数量',
            type: 'pie',
            radius: '50%',
            data: channelStats.map(item => ({
                value: item.count,
                name: item.channel
            }))
        }]
    };
    
    myChart.setOption(option);
}

// 更新趋势图表（使用ECharts）
function updateTrendChart(trendData) {
    const chartDom = document.getElementById('trend-chart');
    const myChart = echarts.init(chartDom);
    
    const option = {
        title: {
            text: '信息收集趋势'
        },
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: trendData.map(item => item.date)
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            name: '文章数量',
            type: 'line',
            data: trendData.map(item => item.count)
        }]
    };
    
    myChart.setOption(option);
}
```

### 2. Vue.js 调用示例

```vue
<template>
  <div class="statistics-dashboard">
    <!-- 汇总统计卡片 -->
    <div class="summary-cards">
      <div class="card">
        <h3>总信息数</h3>
        <p class="number">{{ summaryData.total_count }}</p>
      </div>
      <div class="card">
        <h3>今日新增</h3>
        <p class="number">{{ summaryData.today_count }}</p>
      </div>
    </div>
    
    <!-- 筛选条件 -->
    <div class="filters">
      <el-date-picker
        v-model="filterDate"
        type="date"
        placeholder="选择日期"
        @change="getDetailData"
      />
      <el-select
        v-model="filterChannel"
        placeholder="选择渠道"
        @change="getDetailData"
      >
        <el-option label="全部" value="" />
        <el-option label="微信公众号" value="微信公众号" />
        <el-option label="官网" value="官网" />
      </el-select>
    </div>
    
    <!-- 趋势图表 -->
    <div class="trend-chart">
      <el-select v-model="trendDays" @change="getTrendData">
        <el-option label="最近7天" :value="7" />
        <el-option label="最近30天" :value="30" />
        <el-option label="最近90天" :value="90" />
      </el-select>
      <div id="trend-chart" style="width: 100%; height: 400px;"></div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import * as echarts from 'echarts'

export default {
  name: 'StatisticsDashboard',
  data() {
    return {
      summaryData: {
        total_count: 0,
        today_count: 0,
        today_date: ''
      },
      detailData: {
        channel_stats: [],
        unit_stats: []
      },
      trendData: [],
      filterDate: '',
      filterChannel: '',
      trendDays: 7
    }
  },
  mounted() {
    this.getSummaryData()
    this.getDetailData()
    this.getTrendData()
  },
  methods: {
    async getSummaryData() {
      try {
        const response = await axios.get('/api/faxuan/statistics/info_summary')
        if (response.data.code === 200) {
          this.summaryData = response.data.data
        }
      } catch (error) {
        console.error('获取汇总数据失败:', error)
        this.$message.error('获取汇总数据失败')
      }
    },
    
    async getDetailData() {
      try {
        const params = {}
        if (this.filterDate) {
          params.date = this.filterDate
        }
        if (this.filterChannel) {
          params.channel = this.filterChannel
        }
        
        const response = await axios.get('/api/faxuan/statistics/info_detail', { params })
        if (response.data.code === 200) {
          this.detailData = response.data.data
        }
      } catch (error) {
        console.error('获取详细数据失败:', error)
        this.$message.error('获取详细数据失败')
      }
    },
    
    async getTrendData() {
      try {
        const response = await axios.get('/api/faxuan/statistics/trend', {
          params: { days: this.trendDays }
        })
        if (response.data.code === 200) {
          this.trendData = response.data.data.trend_data
          this.updateTrendChart()
        }
      } catch (error) {
        console.error('获取趋势数据失败:', error)
        this.$message.error('获取趋势数据失败')
      }
    },
    
    updateTrendChart() {
      const chartDom = document.getElementById('trend-chart')
      const myChart = echarts.init(chartDom)
      
      const option = {
        title: {
          text: '信息收集趋势'
        },
        tooltip: {
          trigger: 'axis'
        },
        xAxis: {
          type: 'category',
          data: this.trendData.map(item => item.date)
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '文章数量',
          type: 'line',
          data: this.trendData.map(item => item.count),
          smooth: true
        }]
      }
      
      myChart.setOption(option)
    }
  }
}
</script>
```

## 后端调用示例

### Python 调用示例

```python
import requests
import json
from datetime import date, timedelta

class FaxuanStatisticsClient:
    def __init__(self, base_url, token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }
    
    def get_info_summary(self):
        """获取信息汇总统计"""
        url = f"{self.base_url}/api/faxuan/statistics/info_summary"
        response = requests.get(url, headers=self.headers)
        return response.json()
    
    def get_info_detail(self, date=None, channel=None, unit_name=None):
        """获取信息详细统计"""
        url = f"{self.base_url}/api/faxuan/statistics/info_detail"
        params = {}
        if date:
            params['date'] = date
        if channel:
            params['channel'] = channel
        if unit_name:
            params['unit_name'] = unit_name
        
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()
    
    def get_info_trend(self, days=7):
        """获取信息趋势统计"""
        url = f"{self.base_url}/api/faxuan/statistics/trend"
        params = {'days': days}
        response = requests.get(url, headers=self.headers, params=params)
        return response.json()

# 使用示例
if __name__ == "__main__":
    # 初始化客户端
    client = FaxuanStatisticsClient("http://localhost:5000", "your_token_here")
    
    # 获取汇总统计
    summary = client.get_info_summary()
    print("汇总统计:", json.dumps(summary, indent=2, ensure_ascii=False))
    
    # 获取今日详细统计
    today_detail = client.get_info_detail(date=date.today().strftime('%Y-%m-%d'))
    print("今日详细统计:", json.dumps(today_detail, indent=2, ensure_ascii=False))
    
    # 获取最近30天趋势
    trend = client.get_info_trend(days=30)
    print("30天趋势:", json.dumps(trend, indent=2, ensure_ascii=False))
```

## 定时任务示例

### 使用APScheduler定时更新统计数据

```python
from apscheduler.schedulers.blocking import BlockingScheduler
from web.task.fx_statistics_task import update_daily_statistics, update_current_day_statistics
from datetime import date, timedelta
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def daily_statistics_job():
    """每日统计任务"""
    try:
        yesterday = date.today() - timedelta(days=1)
        update_daily_statistics(yesterday)
        logger.info(f"成功更新 {yesterday} 的统计数据")
    except Exception as e:
        logger.error(f"更新统计数据失败: {str(e)}")

def hourly_statistics_job():
    """每小时统计任务"""
    try:
        update_current_day_statistics()
        logger.info("成功更新当天统计数据")
    except Exception as e:
        logger.error(f"更新当天统计数据失败: {str(e)}")

# 创建调度器
scheduler = BlockingScheduler()

# 添加定时任务
scheduler.add_job(daily_statistics_job, 'cron', hour=2, minute=0)  # 每天凌晨2点
scheduler.add_job(hourly_statistics_job, 'cron', minute=0)  # 每小时整点

# 启动调度器
if __name__ == "__main__":
    logger.info("启动统计数据定时任务")
    scheduler.start()
```

## 数据可视化示例

### 使用matplotlib生成统计图表

```python
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import requests

def generate_trend_chart(token, days=30):
    """生成趋势图表"""
    # 获取趋势数据
    url = "http://localhost:5000/api/faxuan/statistics/trend"
    headers = {'Authorization': f'Bearer {token}'}
    params = {'days': days}
    
    response = requests.get(url, headers=headers, params=params)
    data = response.json()
    
    if data['code'] == 200:
        trend_data = data['data']['trend_data']
        
        # 准备数据
        dates = [datetime.strptime(item['date'], '%Y-%m-%d') for item in trend_data]
        counts = [item['count'] for item in trend_data]
        
        # 创建图表
        plt.figure(figsize=(12, 6))
        plt.plot(dates, counts, marker='o', linewidth=2, markersize=4)
        plt.title(f'最近{days}天信息收集趋势', fontsize=16)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('文章数量', fontsize=12)
        plt.grid(True, alpha=0.3)
        
        # 格式化x轴日期
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=max(1, days//10)))
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(f'trend_{days}days.png', dpi=300, bbox_inches='tight')
        plt.show()
    else:
        print(f"获取数据失败: {data['msg']}")

# 使用示例
if __name__ == "__main__":
    token = "your_token_here"
    generate_trend_chart(token, 30)
```
