-- 创建单位类型映射表
CREATE TABLE IF NOT EXISTS `fx_unit_type_mapping` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `unit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位名称',
  `unit_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '单位类型（市级单位、区级单位、高校、国企）',
  `unit_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '单位分类（法院系统、教育系统等）',
  `is_active` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `update_time` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  `create_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '更新人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_unit_name` (`unit_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '单位类型映射表' ROW_FORMAT = DYNAMIC;

-- 插入默认的单位类型映射数据
INSERT INTO `fx_unit_type_mapping` (`unit_name`, `unit_type`, `unit_category`, `create_by`) VALUES
('市法院', '市级单位', '法院系统', 'system'),
('市教育局', '市级单位', '教育系统', 'system'),
('玄武区司法局', '区级单位', '司法系统', 'system'),
('秦淮区司法局', '区级单位', '司法系统', 'system'),
('栖霞区司法局', '区级单位', '司法系统', 'system'),
('雨花台区司法局', '区级单位', '司法系统', 'system'),
('南京大学', '高校', '高校系统', 'system'),
('东南大学', '高校', '高校系统', 'system'),
('南京市河西新城区国有资产经营控股(集团)有限责任公司', '国企', '国企系统', 'system'),
('中建安装集团有限公司', '国企', '国企系统', 'system')
ON DUPLICATE KEY UPDATE 
  `unit_type` = VALUES(`unit_type`),
  `unit_category` = VALUES(`unit_category`),
  `update_time` = CURRENT_TIMESTAMP,
  `update_by` = 'system';
