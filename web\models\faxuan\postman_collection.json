{"info": {"name": "普法工作管理系统 - 单位类型统计接口", "description": "各类型单位今日发布数统计接口测试集合", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "base_url", "value": "http://localhost:5000", "type": "string"}], "item": [{"name": "1. 登录接口 (获取认证)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123\"\n}"}, "url": {"raw": "{{base_url}}/login", "host": ["{{base_url}}"], "path": ["login"]}, "description": "先登录获取认证信息，后续请求会自动携带Cookie"}, "response": []}, {"name": "2. 获取今日发布数统计", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/faxuan/statistics/unit_type_today", "host": ["{{base_url}}"], "path": ["api", "faxuan", "statistics", "unit_type_today"]}, "description": "获取各类型单位今日发布数统计"}, "response": []}, {"name": "3. 获取指定日期发布数统计", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/faxuan/statistics/unit_type_today?date=2024-01-15", "host": ["{{base_url}}"], "path": ["api", "faxuan", "statistics", "unit_type_today"], "query": [{"key": "date", "value": "2024-01-15", "description": "查询日期，格式YYYY-MM-DD"}]}, "description": "获取指定日期的各类型单位发布数统计"}, "response": []}, {"name": "4. 获取单位类型映射列表", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/faxuan/unit_type/list?page=1&size=10", "host": ["{{base_url}}"], "path": ["api", "faxuan", "unit_type", "list"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "unit_name", "value": "", "disabled": true, "description": "单位名称搜索"}, {"key": "unit_type", "value": "", "disabled": true, "description": "单位类型筛选"}]}, "description": "获取单位类型映射列表，支持分页和搜索"}, "response": []}, {"name": "5. 添加单位类型映射", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"unit_name\": \"测试单位\",\n  \"unit_type\": \"市级单位\",\n  \"unit_category\": \"测试系统\"\n}"}, "url": {"raw": "{{base_url}}/api/faxuan/unit_type/add", "host": ["{{base_url}}"], "path": ["api", "faxuan", "unit_type", "add"]}, "description": "添加新的单位类型映射"}, "response": []}, {"name": "6. 获取单位类型选项", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/api/faxuan/unit_type/types", "host": ["{{base_url}}"], "path": ["api", "faxuan", "unit_type", "types"]}, "description": "获取所有可用的单位类型选项"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 可以在这里添加全局的预请求脚本"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 可以在这里添加全局的测试脚本", "pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has correct structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('msg');", "    pm.expect(jsonData).to.have.property('data');", "});"]}}]}