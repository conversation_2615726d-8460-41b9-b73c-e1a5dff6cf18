<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单位类型统计接口测试</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        .chart-container {
            width: 100%;
            height: 400px;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .info-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .data-display {
            background: #e9ecef;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>普法工作管理系统 - 各类型单位今日发布数统计接口测试</h1>
        </div>

        <div class="info-panel">
            <h3>接口信息</h3>
            <p><strong>接口地址：</strong> /api/faxuan/statistics/unit_type_today</p>
            <p><strong>请求方法：</strong> GET</p>
            <p><strong>参数：</strong> date (可选，格式：YYYY-MM-DD)</p>
        </div>

        <div>
            <button class="btn" onclick="testTodayStats()">测试今日统计</button>
            <button class="btn" onclick="testDateStats()">测试指定日期统计</button>
            <button class="btn" onclick="clearResults()">清空结果</button>
        </div>

        <div id="message"></div>

        <div class="chart-container" id="barChart" style="display: none;"></div>

        <div id="dataDisplay"></div>

        <div id="detailTable"></div>
    </div>

    <script>
        let barChart = null;

        // 初始化图表
        function initChart() {
            const chartDom = document.getElementById('barChart');
            if (barChart) {
                barChart.dispose();
            }
            barChart = echarts.init(chartDom);
        }

        // 测试今日统计
        async function testTodayStats() {
            showMessage('正在获取今日统计数据...', 'info');
            
            try {
                const response = await fetch('/api/faxuan/statistics/unit_type_today');
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage('获取数据成功！', 'success');
                    displayData(result.data);
                    renderChart(result.data);
                    renderDetailTable(result.data.detail_data);
                } else {
                    showMessage(`获取数据失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                showMessage(`请求失败: ${error.message}`, 'error');
            }
        }

        // 测试指定日期统计
        async function testDateStats() {
            const date = prompt('请输入查询日期 (格式: YYYY-MM-DD):', '2024-01-15');
            if (!date) return;

            showMessage(`正在获取 ${date} 的统计数据...`, 'info');
            
            try {
                const response = await fetch(`/api/faxuan/statistics/unit_type_today?date=${date}`);
                const result = await response.json();
                
                if (result.code === 200) {
                    showMessage(`获取 ${date} 数据成功！`, 'success');
                    displayData(result.data);
                    renderChart(result.data);
                    renderDetailTable(result.data.detail_data);
                } else {
                    showMessage(`获取数据失败: ${result.msg}`, 'error');
                }
            } catch (error) {
                showMessage(`请求失败: ${error.message}`, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.className = type;
            messageDiv.textContent = message;
        }

        // 显示数据
        function displayData(data) {
            const dataDisplay = document.getElementById('dataDisplay');
            dataDisplay.innerHTML = `<h3>返回数据：</h3>${JSON.stringify(data, null, 2)}`;
        }

        // 渲染图表
        function renderChart(data) {
            const chartContainer = document.getElementById('barChart');
            chartContainer.style.display = 'block';
            
            initChart();
            
            const option = {
                title: {
                    text: `各类型单位发布数统计 (${data.query_date})`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: data.x_axis_data,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '发布数量'
                },
                series: [{
                    name: '发布数量',
                    type: 'bar',
                    data: data.y_axis_data,
                    itemStyle: {
                        color: '#5470c6'
                    },
                    label: {
                        show: true,
                        position: 'top'
                    }
                }]
            };
            
            barChart.setOption(option);
        }

        // 渲染详细数据表格
        function renderDetailTable(detailData) {
            const tableContainer = document.getElementById('detailTable');
            
            if (!detailData || detailData.length === 0) {
                tableContainer.innerHTML = '<h3>详细数据：</h3><p>暂无数据</p>';
                return;
            }

            let tableHtml = `
                <h3>详细数据：</h3>
                <table>
                    <thead>
                        <tr>
                            <th>单位类型</th>
                            <th>单位名称</th>
                            <th>发布数量</th>
                            <th>最新发布时间</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            detailData.forEach(item => {
                tableHtml += `
                    <tr>
                        <td>${item.unit_type}</td>
                        <td>${item.unit_name}</td>
                        <td>${item.count}</td>
                        <td>${item.last_publish_time || '无'}</td>
                    </tr>
                `;
            });

            tableHtml += '</tbody></table>';
            tableContainer.innerHTML = tableHtml;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('message').innerHTML = '';
            document.getElementById('dataDisplay').innerHTML = '';
            document.getElementById('detailTable').innerHTML = '';
            document.getElementById('barChart').style.display = 'none';
            
            if (barChart) {
                barChart.dispose();
                barChart = null;
            }
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            showMessage('页面加载完成，可以开始测试接口', 'success');
        };
    </script>
</body>
</html>
