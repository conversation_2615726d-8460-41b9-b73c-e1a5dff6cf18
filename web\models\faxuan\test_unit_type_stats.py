#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单位类型统计接口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from datetime import datetime, date
from web import create_app, db
from web.models.faxuan.FXArticleRecords import FxArticleRecords
from web.models.faxuan.FXUnitTypeMapping import FxUnitTypeMapping

def test_unit_type_mapping():
    """测试单位类型映射功能"""
    app = create_app()
    
    with app.app_context():
        # 测试单位类型推断
        test_units = [
            '市法院',
            '市教育局', 
            '玄武区司法局',
            '南京大学',
            '东南大学',
            '南京市河西新城区国有资产经营控股(集团)有限责任公司',
            '中建安装集团有限公司',
            '未知单位'
        ]
        
        print("=== 单位类型推断测试 ===")
        for unit_name in test_units:
            unit_type = FxUnitTypeMapping.get_unit_type_by_name(unit_name)
            print(f"{unit_name} -> {unit_type}")
        
        print("\n=== 数据库映射测试 ===")
        # 初始化默认映射
        try:
            FxUnitTypeMapping.init_default_mappings()
            print("默认映射初始化成功")
        except Exception as e:
            print(f"默认映射初始化失败: {e}")
        
        # 查询所有映射
        mappings = FxUnitTypeMapping.query.all()
        print(f"当前映射数量: {len(mappings)}")
        for mapping in mappings:
            print(f"  {mapping.unit_name} -> {mapping.unit_type} ({mapping.unit_category})")

def test_article_records():
    """测试文章记录的单位类型获取"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 文章记录单位类型测试 ===")
        
        # 查询一些文章记录
        articles = FxArticleRecords.query.limit(10).all()
        
        for article in articles:
            unit_type = article.get_unit_type()
            print(f"{article.unit_name} -> {unit_type}")

def create_test_data():
    """创建测试数据"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 创建测试数据 ===")
        
        # 创建今日测试数据
        today = datetime.now()
        
        test_articles = [
            {
                'unit_name': '市法院',
                'article_title': '市法院今日普法宣传',
                'article_content': '测试内容',
                'article_id': f'test_court_{today.strftime("%Y%m%d")}_001',
                'crawl_channel': '官网',
                'crawl_time': today,
                'create_time': today
            },
            {
                'unit_name': '玄武区司法局',
                'article_title': '玄武区司法局普法活动',
                'article_content': '测试内容',
                'article_id': f'test_xuanwu_{today.strftime("%Y%m%d")}_001',
                'crawl_channel': '官网',
                'crawl_time': today,
                'create_time': today
            },
            {
                'unit_name': '南京大学',
                'article_title': '南京大学法律讲座',
                'article_content': '测试内容',
                'article_id': f'test_nju_{today.strftime("%Y%m%d")}_001',
                'crawl_channel': '官网',
                'crawl_time': today,
                'create_time': today
            },
            {
                'unit_name': '中建安装集团有限公司',
                'article_title': '企业法律培训',
                'article_content': '测试内容',
                'article_id': f'test_zjaz_{today.strftime("%Y%m%d")}_001',
                'crawl_channel': '官网',
                'crawl_time': today,
                'create_time': today
            }
        ]
        
        for article_data in test_articles:
            # 检查是否已存在
            existing = FxArticleRecords.query.filter_by(article_id=article_data['article_id']).first()
            if not existing:
                article = FxArticleRecords(**article_data)
                db.session.add(article)
        
        try:
            db.session.commit()
            print("测试数据创建成功")
        except Exception as e:
            db.session.rollback()
            print(f"测试数据创建失败: {e}")

def test_statistics():
    """测试统计功能"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 统计功能测试 ===")
        
        # 模拟统计接口逻辑
        from datetime import date
        from sqlalchemy import and_
        
        target_date = date.today()
        target_start = datetime.combine(target_date, datetime.min.time())
        target_end = datetime.combine(target_date, datetime.max.time())
        
        # 查询当日发布的文章记录
        articles = db.session.query(FxArticleRecords).filter(
            and_(
                FxArticleRecords.create_time >= target_start,
                FxArticleRecords.create_time <= target_end
            )
        ).all()
        
        print(f"今日文章总数: {len(articles)}")
        
        # 统计各类型单位发布数
        unit_type_stats = {}
        
        for article in articles:
            unit_type = article.get_unit_type()
            if unit_type not in unit_type_stats:
                unit_type_stats[unit_type] = 0
            unit_type_stats[unit_type] += 1
        
        print("各类型单位发布数统计:")
        for unit_type, count in unit_type_stats.items():
            print(f"  {unit_type}: {count}")
        
        # 构建标准格式数据
        standard_types = ['市级单位', '区级单位', '国企', '高校', '其他']
        x_axis_data = []
        y_axis_data = []
        
        for unit_type in standard_types:
            count = unit_type_stats.get(unit_type, 0)
            x_axis_data.append(unit_type)
            y_axis_data.append(count)
        
        print(f"\nECharts数据格式:")
        print(f"x轴数据: {x_axis_data}")
        print(f"y轴数据: {y_axis_data}")

if __name__ == '__main__':
    print("开始测试单位类型统计功能...")
    
    # 测试单位类型映射
    test_unit_type_mapping()
    
    # 测试文章记录
    test_article_records()
    
    # 创建测试数据
    create_test_data()
    
    # 测试统计功能
    test_statistics()
    
    print("\n测试完成！")
